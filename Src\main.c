/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "i2c.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
//#include "e70_config.h"
#include <string.h>
#include <stdio.h>
#include "mlx90393.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// MLX90393相关变量
//static MLX90393_Handle_t mlx_handle;
//volatile uint8_t mlx90393_interrupt_flag = 0;  // MLX90393中断标志位

//// 业务流程控制变量
//static uint8_t e70_first_init_done = 0;  // E70首次初始化标志
//static uint32_t e70_listen_start_time = 0;  // E70监听开始时间
//static uint32_t e70_send_start_time = 0;    // E70发送开始时间
//static uint8_t e70_send_retry_count = 0;    // E70发送重试次数
//static uint16_t saved_battery_voltage = 0;  // 保存的电池电压值

//// 业务流程状态枚举
//typedef enum {
//    STATE_WAKE_UP = 0,           // 唤醒阶段
//    STATE_VOLTAGE_CHECK,         // 电压检测阶段
//    STATE_E70_LISTEN,           // E70监听阶段
//    STATE_E70_SEND_DATA,        // E70发送数据阶段
//    STATE_E70_WAIT_ACK,         // E70等待确认阶段
//    STATE_PREPARE_SLEEP         // 准备休眠阶段
//} BusinessState_t;

//static BusinessState_t current_state = STATE_WAKE_UP;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
uint8_t txmp[20] = {0xFC, 0xFD, 0x77, 0x88, 0xFD}; // 固定发送数据
uint8_t rxmp[20];
uint8_t rx_complete_flag = 0; // 接收完成标志
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C1_Init();
  MX_LPUART1_UART_Init();
  MX_USART2_UART_Init();
  MX_ADC_Init();
  /* USER CODE BEGIN 2 */
	// 启动UART接收中断，接收5字节
	HAL_UART_Receive_IT(&hlpuart1, rxmp, 5);

	printf("=== STM32L031G6 UART Test ===\r\n");
	printf("Waiting for 5 bytes data...\r\n");
	LED_ON;

	// E70首次初始化（生命周期内只执行一次）
//	if (!e70_first_init_done) {
////		printf("=== E70 First Time Initialization ===\r\n");
//		uint8_t init_result = E70_InitializeConfig(E70_MODULE_ADDRESS, 10, 1);
//		if (!init_result) {
//			printf("E70 Init FAILED!\r\n");
//			Error_Handler();
//		}
//		E70_EnterCommMode();
////		printf("=== E70 Configuration Complete ===\r\n");
//		e70_first_init_done = 1;  // 标记首次初始化完成
//	}

	// 初始化时关闭所有模块电源
//	MLX90393_PW_OFF;
//	RF_PWR_OFF;   // 关闭通信模块
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
		// 发送固定数据：FC FD 55 66 FD
		HAL_UART_Transmit(&hlpuart1, txmp, 5, 10);
		printf("Sent: FC FD 77 88 FD\r\n");

		// 检查是否接收到5字节数据
		if(rx_complete_flag == 1)
		{
			rx_complete_flag = 0; // 清除标志

			// 打印接收到的数据
			printf("Received: ");
			for(int i = 0; i < 5; i++)
			{
				printf("%02X ", rxmp[i]);
			}
			printf("\r\n");

			// 重新启动接收
			HAL_UART_Receive_IT(&hlpuart1, rxmp, 5);
		}

		HAL_Delay(1000); // 延时1秒

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_MSI;
  RCC_OscInitStruct.MSIState = RCC_MSI_ON;
  RCC_OscInitStruct.MSICalibrationValue = 0;
  RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_5;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART2|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_I2C1;
  PeriphClkInit.Usart2ClockSelection = RCC_USART2CLKSOURCE_SYSCLK;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	if(huart == &hlpuart1)
	{
		rx_complete_flag = 1; // 设置接收完成标志
	}
}
/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
