# ADC电池电压采样使用指南

## 🎯 功能概述

本模块实现了基于STM32L0系列的ADC电池电压采样功能，具有以下特点：
- 使用内部参考电压(VREFINT)进行VDD校正
- 支持2:1分压电阻的硬件设计
- 提供校准系数和偏移量设置
- 多次采样取平均值提高精度
- 自动处理ADC初始化和校准

## 🔧 硬件连接

### ADC通道配置
- **PA0 (ADC_IN0)**: 电池电压输入（通过2:1分压电阻）- 当前配置
- **内部VREFINT**: 用于VDD校正

**注意**: 如果你的硬件使用PA6而不是PA0，需要在CubeMX中修改ADC配置：
1. 打开CubeMX项目
2. 在ADC1配置中，将Channel 0改为Channel 6
3. 重新生成代码

### 分压电路
```
电池正极 ----[R1]----+----[R2]---- GND
                     |
                   PA6 (ADC_IN6)
```
- R1 = R2（相等阻值，形成2:1分压）
- 推荐阻值：10kΩ - 100kΩ

## 📊 API接口

### 1. 读取ADC原始值
```c
uint16_t ADC_ReadBatteryVoltage(void);
```
- **功能**: 读取电池电压的ADC原始值
- **返回**: 12位ADC值 (0-4095)
- **说明**: 自动进行多次采样并取平均值

### 2. 转换为实际电压
```c
float ADC_ConvertToVoltage(uint16_t adc_value);
```
- **功能**: 将ADC值转换为实际电池电压
- **参数**: ADC原始值
- **返回**: 电池电压值(V)
- **说明**: 自动应用VDD校正和用户校准参数

### 3. 完整使用示例
```c
// 读取电池电压
uint16_t adc_value = ADC_ReadBatteryVoltage();
float voltage_v = ADC_ConvertToVoltage(adc_value);
uint16_t voltage_mv = (uint16_t)(voltage_v * 1000);

printf("Battery: ADC=%d, Voltage=%.3fV (%dmV)\r\n",
       adc_value, voltage_v, voltage_mv);
```

## ⚙️ 校准配置

### 编译时配置
在编译选项中定义校准参数：
```bash
# 校准系数（默认1.0）
-DBATTERY_VOLTAGE_CALIBRATION_FACTOR=1.05f

# 偏移量（默认0.0V）
-DBATTERY_VOLTAGE_OFFSET=0.02f
```

### 代码中配置
在 `e70_config.h` 中修改：
```c
#define BATTERY_VOLTAGE_CALIBRATION_FACTOR  1.05f   // 校准系数
#define BATTERY_VOLTAGE_OFFSET              0.02f   // 偏移量(V)
```

### 校准方法
1. **测量实际电池电压**: 使用万用表测量电池实际电压 V_real
2. **读取系统显示电压**: 运行程序获取显示电压 V_display
3. **计算校准系数**: `CALIBRATION_FACTOR = V_real / V_display`
4. **应用校准**: 重新编译并测试

### 校准示例
```
实际电压: 4.15V
显示电压: 4.08V
校准系数: 4.15 / 4.08 = 1.017

配置: #define BATTERY_VOLTAGE_CALIBRATION_FACTOR 1.017f
```

## 🔬 技术原理

### VDD校正原理
1. **问题**: STM32的ADC参考电压是VDD，当VDD变化时影响测量精度
2. **解决**: 使用内部1.2V参考电压(VREFINT)校正VDD变化
3. **公式**: `实际VDD = 3.0V × VREFINT_CAL / VREFINT_ADC`

### 电压计算公式
```c
// 基础电压计算
voltage_base = (adc_value * actual_vdd / 4096.0f) * 2.0f;

// 应用用户校准
voltage_final = voltage_base * CALIBRATION_FACTOR + OFFSET;
```

### 采样策略
- **多次采样**: 每次读取20个样本取平均值
- **通道交替**: CH6 → VREFINT → TEMP 循环采样
- **ADC校准**: 每次采样前进行ADC校准

## 📈 性能特点

### 精度指标
- **分辨率**: 12位 (4096级)
- **理论精度**: ±1LSB (约±0.8mV @ 3.3V)
- **实际精度**: ±10mV (经校准后)

### 测量范围
- **ADC输入范围**: 0 - VDD (通常3.3V)
- **电池电压范围**: 0 - 6.6V (2:1分压)
- **推荐工作范围**: 2.5V - 4.5V

### 采样时间
- **单次采样**: ~100ms (包含20次平均)
- **转换时间**: ~50ms (VREFINT校正)
- **总耗时**: ~150ms

## 🚨 注意事项

### 硬件要求
1. **分压电阻**: 必须使用相等阻值的精密电阻
2. **输入保护**: 确保输入电压不超过VDD+0.3V
3. **滤波电容**: 建议在ADC输入端添加小容量滤波电容

### 软件注意
1. **ADC初始化**: 确保MX_ADC_Init()正确配置3通道
2. **DMA配置**: 需要正确配置DMA用于多通道采样
3. **时序要求**: 采样前需要足够的稳定时间

### 校准建议
1. **温度补偿**: 在不同温度下进行校准验证
2. **老化补偿**: 定期重新校准以补偿器件老化
3. **批量校准**: 生产时每个设备单独校准

## 🔧 故障排除

### 常见问题
1. **读数为0**: 检查ADC通道配置和硬件连接
2. **读数异常**: 检查分压电阻和参考电压
3. **精度不够**: 调整校准参数或检查硬件噪声

### 调试方法
```c
// 调试输出
printf("ADC Debug: Raw=%d, VRefInt=%d, VDD=%.3fV\r\n",
       adc_raw, vrefint_raw, actual_vdd);
```

## 📝 集成示例

### 在E70通信中使用
```c
// 在发送数据时读取电池电压
uint16_t adc_value = ADC_ReadBatteryVoltage();
float voltage_v = ADC_ConvertToVoltage(adc_value);
uint16_t battery_voltage_mv = (uint16_t)(voltage_v * 1000);

// 发送设备信息（包含电池电压）
E70_SendDeviceInfo(MCU_DEVICE_ID, battery_voltage_mv);
```

### 定期监测
```c
// 主循环中定期检测电池电压
if (current_time - last_voltage_check >= 60000) {  // 每分钟检测
    last_voltage_check = current_time;

    uint16_t adc_value = ADC_ReadBatteryVoltage();
    float voltage = ADC_ConvertToVoltage(adc_value);

    if (voltage < 3.0f) {
        printf("Low battery warning: %.2fV\r\n", voltage);
        // 进入低功耗模式或发送警告
    }
}
```

---

**版本**: v1.0
**更新日期**: 2025年1月
**状态**: 已验证可用
