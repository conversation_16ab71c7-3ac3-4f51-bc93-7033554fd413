# STM32L031 低功耗模式测试步骤

## 🔧 编译错误修复

✅ **hdma_adc未定义错误已修复**
- 使用`hadc.DMA_Handle`来访问DMA句柄
- 添加了空指针检查确保安全性

## 🧪 测试步骤

### 1. 编译并下载代码
确保代码无编译错误，下载到STM32L031G6U6

### 2. 连接电流表
- 在VDD电源线上串联电流表
- 确保能够测量微安级电流

### 3. 观察串口输出
运行后应该看到类似输出：
```
=== Device Wake Up ===
MLX90393 Power: OFF
Waiting 1 second for ADC stabilization...
Battery Voltage: 3.826V (3826mV)
LED blinking 3 times...
Initializing MLX90393...
MLX90393 Init: SUCCESS
MLX90393 WOC Mode: ENABLED
INT pin (PB0) ready for interrupts
Preparing to enter STOP mode...
Device will wake up on MLX90393 interrupt (PB0)
Debug: RCC->AHBENR = 0x????????
Debug: RCC->APB1ENR = 0x????????
Debug: RCC->APB2ENR = 0x????????
Debug: NVIC pending interrupts: 0x????????
```

### 4. 检查调试寄存器值

**期望的寄存器值**：
- `RCC->AHBENR`: 应该只有GPIO时钟启用 (约0x00000007)
- `RCC->APB1ENR`: 应该接近0x00000000
- `RCC->APB2ENR`: 应该接近0x00000000  
- `NVIC pending interrupts`: 应该是0x00000000

**如果寄存器值不正常**：
- APB1ENR/APB2ENR不为0 → 某些外设时钟未关闭
- NVIC挂起中断不为0 → 有未处理的中断阻止休眠

### 5. 观察电流变化

**正常情况下**：
- 设备工作时：2-5mA
- 进入STOP模式后：应该降至10μA以下
- 如果电流仍然是32mA，说明未真正进入STOP模式

### 6. 测试唤醒功能
- 使用磁铁靠近MLX90393传感器
- 应该看到设备唤醒并输出：
```
*** WAKE UP from STOP mode ***
```

## 🔍 问题排查

### 如果电流仍然很高（>1mA）

1. **检查RF模块**：
   ```c
   // 确保RF_PWR_OFF宏正确定义和执行
   RF_PWR_OFF;
   ```

2. **检查GPIO状态**：
   - 确保所有输出引脚处于正确状态
   - 检查是否有上拉电阻导致漏电流

3. **逐步排除法**：
   - 注释掉MLX90393初始化，只测试基本休眠
   - 逐个添加外设，找出问题源

### 如果无法唤醒

1. **检查PB0配置**：
   ```c
   // 确保PB0配置为外部中断
   GPIO_InitStruct.Pin = GPIO_PIN_0;
   GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING;
   GPIO_InitStruct.Pull = GPIO_NOPULL;
   ```

2. **检查NVIC配置**：
   ```c
   // 确保EXTI0_1中断启用
   HAL_NVIC_SetPriority(EXTI0_1_IRQn, 0, 0);
   HAL_NVIC_EnableIRQ(EXTI0_1_IRQn);
   ```

3. **检查MLX90393连接**：
   - 确保INT引脚正确连接到PB0
   - 确保MLX90393电源和I2C连接正常

## 📊 预期测试结果

### 成功的低功耗模式：
- **休眠电流**: < 10μA
- **工作电流**: 2-5mA
- **唤醒时间**: < 10μs
- **功能正常**: 所有外设在唤醒后正常工作

### 调试输出示例：
```
Debug: RCC->AHBENR = 0x00000007    ✅ 只有GPIO时钟
Debug: RCC->APB1ENR = 0x00000000   ✅ APB1外设时钟全关
Debug: RCC->APB2ENR = 0x00000000   ✅ APB2外设时钟全关
Debug: NVIC pending interrupts: 0x00000000  ✅ 无挂起中断
```

## 🛠️ 进一步优化建议

如果基本低功耗模式工作正常，可以考虑：

1. **降低系统时钟**：
   - 从RCC_MSIRANGE_5 (2.097MHz) 降至 RCC_MSIRANGE_4 (1.048MHz)
   - 进一步降低运行功耗

2. **优化GPIO配置**：
   - 检查所有未使用的引脚
   - 确保没有浮空输入引脚

3. **外部硬件优化**：
   - 检查PCB上的上拉/下拉电阻
   - 确保外部模块完全断电

## ⚠️ 重要提醒

1. **不要禁用EXTI0_1_IRQn中断** - 这是唤醒中断！
2. **确保RF_PWR_OFF正确执行** - 外部模块可能是高功耗源
3. **检查硬件连接** - 确保没有短路或异常连接
4. **使用合适的电流表** - 确保能测量微安级电流

---

**测试完成后，请分享调试输出的寄存器值，这将帮助进一步定位问题！**
