/**
 * @file    e70_config.h
 * @brief   E70 LoRa模块配置头文件 - 实际验证可用版本
 * <AUTHOR> Name
 * @date    2025年1月
 * @note    此版本已通过实际硬件测试验证，所有基本功能正常工作
 *
 * 重要提醒：
 * 1. 必须使用配置模式5 (101) 进行配置，模式3 (011) 无响应
 * 2. 必须在上电前设置模式引脚，不能运行时切换
 * 3. 支持的命令：C1读配置(✅), C0写配置(✅), C3读版本(❌), C4复位(❌)
 */

#ifndef __E70_CONFIG_H__
#define __E70_CONFIG_H__

#include "stm32l0xx_hal.h"
#include <stdint.h>

// 模块工作模式定义 (根据实际测试验证 - 2025年1月)
typedef enum {
    E70_MODE_DISABLE = 0,       // 禁用模式 (000) - 禁用一切通信
    E70_MODE_TRANS = 1,         // 连续通信模式 (001) - 正常通信模式 ✅
    E70_MODE_POWER_SAVE = 2,    // 省电模式 (010)
    E70_MODE_CONFIG3 = 3,       // 配置模式3 (011) - 实测无响应，不可用 ❌
    E70_MODE_RSSI = 4,          // RSSI模式 (100)
    E70_MODE_CONFIG5 = 5,       // 配置模式5 (101) - 实测有效的配置模式 ✅
    E70_MODE_RESERVED = 6,      // 保留模式 (110)
    E70_MODE_DEEP_SLEEP = 7     // 深度休眠模式 (111)
} E70_Mode_t;

// 发射功率定义
typedef enum {
    E70_POWER_14DBM = 0x00, // 14dBm (默认)
    E70_POWER_10DBM = 0x01, // 10dBm
    E70_POWER_7DBM = 0x02,  // 7dBm
    E70_POWER_4DBM = 0x03   // 4dBm
} E70_Power_t;

// 配置参数结构体
typedef struct {
    uint8_t head;           // 命令头 (0xC0保存/0xC2不保存)
    uint8_t addh;           // 地址高字节
    uint8_t addl;           // 地址低字节
    uint8_t sped;           // 速度配置
    uint8_t chan;           // 信道配置
    uint8_t option;         // 选项配置
} E70_Config_t;

// GPIO引脚由 CubeMX 在 main.h 中定义为 M0_Pin/M1_Pin/M2_Pin，对应端口为 M0_GPIO_Port/M1_GPIO_Port/M2_GPIO_Port
// 这里不再重复定义，避免冲突和重复。

// 模块默认配置 (根据数据手册E70-400MT14S出厂参数)
#define E70_DEFAULT_HEAD    0xC0    // 保存命令头
#define E70_DEFAULT_ADDH    0x00    // 地址高字节
#define E70_DEFAULT_ADDL    0x00    // 地址低字节
#define E70_DEFAULT_SPED    0x18    // 串口9600bps(011), 空中2.5k(000), 8N1(00)
#define E70_DEFAULT_CHAN    0x04    // 信道4, 64字节包长(010)
#define E70_DEFAULT_OPTION  0x1C    // 默认选项配置

// 超时定义
#define E70_CONFIG_TIMEOUT  1000    // 配置超时时间(ms)
#define E70_AUX_TIMEOUT     2000    // AUX等待超时时间(ms)

// 外部变量声明
extern UART_HandleTypeDef hlpuart1;    // 与E70通信的串口
extern UART_HandleTypeDef huart2;      // 调试打印串口

// 主要功能函数
void E70_Init(void);
HAL_StatusTypeDef E70_SetAddress(uint16_t address);
HAL_StatusTypeDef E70_SetChannel(uint8_t channel);
HAL_StatusTypeDef E70_SetTxPower(E70_Power_t power);
HAL_StatusTypeDef E70_GetVersion(uint8_t *version, uint8_t len);

// 通信协议定义
#define E70_SEND_HEADER1        0xFA
#define E70_SEND_HEADER2        0xFB
#define E70_RECV_HEADER1        0xFC
#define E70_RECV_HEADER2        0xFD
#define E70_RECV_ACK1           0x55    // 阶段3监听时的ACK确认
#define E70_RECV_ACK2           0x66    // 阶段3监听时的ACK确认
#define E70_RECV_CONFIRM1       0x77    // 阶段5发送后的确认回复
#define E70_RECV_CONFIRM2       0x88    // 阶段5发送后的确认回复
#define E70_PACKET_END          0xFD

#define E70_SEND_PACKET_SIZE    8   // FA FB + ID(2) + 电压(2) + FD = 8字节
#define E70_RECV_PACKET_SIZE    6   // FC FD + 55 66 + FD = 6字节

// 应用层设备ID定义（每个设备生产时不同，用于业务逻辑识别）
#ifndef MCU_DEVICE_ID
#define MCU_DEVICE_ID           0x0001  // 默认单片机设备ID，生产时每个设备不同
#endif

// E70模块网络地址（用于LoRa组网，可以多个设备使用相同地址）
#ifndef E70_MODULE_ADDRESS
#define E70_MODULE_ADDRESS      0x6666  // 默认E70模块地址，用于组网
#endif

// 通信数据包结构
typedef struct {
    uint8_t header1;        // 0xFA
    uint8_t header2;        // 0xFB
    uint16_t mcu_device_id; // 单片机设备ID (高位在前) - 用于业务逻辑识别
    uint16_t battery_voltage; // 电池电压
    uint8_t end;            // 0xFD
} __attribute__((packed)) E70_SendPacket_t;

typedef struct {
    uint8_t header1;        // 0xFC
    uint8_t header2;        // 0xFD
    uint8_t ack1;           // 0x55 (阶段3监听ACK)
    uint8_t ack2;           // 0x66 (阶段3监听ACK)
    uint8_t end;            // 0xFD
} __attribute__((packed)) E70_RecvPacket_t;

typedef struct {
    uint8_t header1;        // 0xFC
    uint8_t header2;        // 0xFD
    uint8_t confirm1;       // 0x77 (阶段5确认回复)
    uint8_t confirm2;       // 0x88 (阶段5确认回复)
    uint8_t end;            // 0xFD
} __attribute__((packed)) E70_ConfirmPacket_t;

// 配置函数（初始化时调用一次）
uint8_t E70_InitializeConfig(uint16_t e70_module_address, uint8_t channel, uint8_t power);

// 通信函数
uint8_t E70_EnterCommMode(void);
uint8_t E70_SendDeviceInfo(uint16_t mcu_device_id, uint16_t battery_voltage);
uint8_t E70_CheckReceiveAck(void);          // 检查阶段3的ACK (FC FD + 55 66 + FD)
uint8_t E70_CheckReceiveConfirm(void);      // 检查阶段5的确认 (FC FD + 77 88 + FD)
void E70_ClearRxBuffer(void);               // 清空接收缓冲区
void E70_StartContinuousRx(void);
void E70_StopContinuousRx(void);

// 内部测试函数
void E70_StartRx(void);
void E70_StopRx(void);
void E70_PrintRxBuffer(void);
void E70_ParseConfigData(void);
uint8_t E70_WriteSimpleConfig(uint16_t address, uint8_t channel, uint8_t power);
void E70_ReadAndPrintConfig(void);

// ADC电池电压采样功能
uint16_t ADC_ReadBatteryVoltage(void);      // 返回电池电压(mV)
float ADC_ConvertToVoltage(uint16_t voltage_mv);  // mV转V（兼容性函数）

// ADC电压校准宏定义（可根据实际测量调整）
#ifndef BATTERY_VOLTAGE_CALIBRATION_FACTOR
#define BATTERY_VOLTAGE_CALIBRATION_FACTOR  1.0f    // 电压校准系数，默认1.0（无校准）
#endif

#ifndef BATTERY_VOLTAGE_OFFSET
#define BATTERY_VOLTAGE_OFFSET              0.06f    // 电压偏移量，默认0.0V
#endif

// ADC采样配置
#define ADC_SAMPLE_COUNT                    50      // ADC采样次数
#define ADC_VREFINT_CAL_VREF                3000    // 校准时的参考电压(mV)
#define ADC_VREFINT_CAL_ADDR                ((uint16_t*)0x1FF80078)  // VREFINT校准地址
#define ADC_VREFINT_CAL_VALUE               (*ADC_VREFINT_CAL_ADDR)  // VREFINT校准值

// 当前ADC通道配置（根据实际硬件连接）
// 注意：当前CubeMX配置为PA0(ADC_IN0)，如果硬件是PA6请修改CubeMX配置
#define BATTERY_ADC_CHANNEL                 ADC_CHANNEL_0  // PA0 - 当前配置
// #define BATTERY_ADC_CHANNEL              ADC_CHANNEL_6  // PA6 - 如果硬件使用PA6

// 内部使用的函数（外部请仅使用 E70_SetModeWithPowerCycle 进行模式切换）
void E70_SetModeWithPowerCycle(E70_Mode_t mode);
HAL_StatusTypeDef E70_ReadConfig(E70_Config_t *config);
HAL_StatusTypeDef E70_WriteConfig(E70_Config_t *config, uint8_t save);

// 串口中断回调函数
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart);

#endif /* __E70_CONFIG_H__ */



