# STM32L031 低功耗模式问题排查指南

## 🔍 问题现象
- 设备显示进入STOP模式，但电流仍然是32mA
- 没有达到预期的微安级功耗

## 🚨 常见原因分析

### 1. 外设时钟未完全关闭
**问题**: 某些外设时钟仍在运行，消耗功耗
**解决方案**:
```c
// 关闭所有不必要的外设时钟
__HAL_RCC_USART2_CLK_DISABLE();
__HAL_RCC_LPUART1_CLK_DISABLE();
__HAL_RCC_ADC1_CLK_DISABLE();
__HAL_RCC_DMA1_CLK_DISABLE();
__HAL_RCC_I2C1_CLK_DISABLE();
```

### 2. DMA传输未停止
**问题**: DMA在后台持续运行，阻止进入低功耗模式
**解决方案**:
```c
// 完全停止ADC和DMA
HAL_ADC_Stop_DMA(&hadc);
HAL_ADC_Stop(&hadc);
HAL_DMA_Abort(&hdma_adc);
HAL_NVIC_DisableIRQ(DMA1_Channel1_IRQn);
HAL_NVIC_DisableIRQ(ADC1_COMP_IRQn);
```

### 3. UART中断未禁用
**问题**: UART中断保持激活状态
**解决方案**:
```c
// 停止并禁用UART中断
HAL_UART_AbortReceive_IT(&huart2);
HAL_UART_AbortReceive_IT(&hlpuart1);
HAL_NVIC_DisableIRQ(USART2_IRQn);
HAL_NVIC_DisableIRQ(LPUART1_IRQn);

// ⚠️ 重要：不要禁用EXTI0_1_IRQn，这是PB0唤醒中断！
// HAL_NVIC_DisableIRQ(EXTI0_1_IRQn); // 绝对不要这样做！
```

### 4. GPIO配置不当
**问题**: 未使用的GPIO引脚配置为数字输入，可能产生漏电流
**解决方案**: 将所有未使用的GPIO配置为模拟输入模式
```c
GPIO_InitTypeDef GPIO_InitStruct = {0};
GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
GPIO_InitStruct.Pull = GPIO_NOPULL;

// 配置未使用的引脚
GPIO_InitStruct.Pin = GPIO_PIN_1 | GPIO_PIN_7 | GPIO_PIN_9 | GPIO_PIN_10;
HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
```

### 5. 挂起的中断未清除
**问题**: 有挂起的中断阻止进入STOP模式
**解决方案**:
```c
// 清除所有挂起的中断标志
__HAL_PWR_CLEAR_FLAG(PWR_FLAG_WU);
```

### 6. 外部模块功耗
**问题**: 外部通信模块（RF模块）仍在消耗功耗
**解决方案**: 确保外部模块电源完全关闭
```c
RF_PWR_OFF; // 关闭通信模块
```

## 🔧 修改后的代码特点

### 进入休眠前的完整准备
```c
// 1. 关闭外部模块
RF_PWR_OFF;

// 2. 停止所有外设
HAL_UART_AbortReceive_IT(&huart2);
HAL_UART_AbortReceive_IT(&hlpuart1);
HAL_ADC_Stop_DMA(&hadc);
HAL_ADC_Stop(&hadc);
HAL_DMA_Abort(&hdma_adc);

// 3. 禁用所有中断
HAL_NVIC_DisableIRQ(USART2_IRQn);
HAL_NVIC_DisableIRQ(LPUART1_IRQn);
HAL_NVIC_DisableIRQ(DMA1_Channel1_IRQn);
HAL_NVIC_DisableIRQ(ADC1_COMP_IRQn);

// 4. 关闭外设时钟
__HAL_RCC_USART2_CLK_DISABLE();
__HAL_RCC_LPUART1_CLK_DISABLE();
__HAL_RCC_ADC1_CLK_DISABLE();
__HAL_RCC_DMA1_CLK_DISABLE();
__HAL_RCC_I2C1_CLK_DISABLE();

// 5. 配置GPIO为模拟输入
// (详见代码实现)

// 6. 清除挂起中断
__HAL_PWR_CLEAR_FLAG(PWR_FLAG_WU);

// 7. 进入STOP模式
HAL_SuspendTick();
HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
```

### 唤醒后的完整恢复
```c
// 1. 恢复系统时钟
SystemClock_Config();
HAL_ResumeTick();

// 2. 重新启用外设时钟
__HAL_RCC_USART2_CLK_ENABLE();
__HAL_RCC_LPUART1_CLK_ENABLE();
__HAL_RCC_ADC1_CLK_ENABLE();
__HAL_RCC_DMA1_CLK_ENABLE();
__HAL_RCC_I2C1_CLK_ENABLE();

// 3. 重新配置GPIO
MX_GPIO_Init();

// 4. 重新初始化外设
MX_USART2_UART_Init();
MX_LPUART1_UART_Init();
MX_ADC_Init();
MX_I2C1_Init();

// 5. 重新启用中断
HAL_NVIC_SetPriority(USART2_IRQn, 0, 0);
HAL_NVIC_EnableIRQ(USART2_IRQn);
HAL_NVIC_SetPriority(LPUART1_IRQn, 0, 0);
HAL_NVIC_EnableIRQ(LPUART1_IRQn);
```

## 📊 调试信息

代码中添加了调试输出，帮助识别问题：
```c
printf("Debug: RCC->AHBENR = 0x%08lX\r\n", RCC->AHBENR);
printf("Debug: RCC->APB1ENR = 0x%08lX\r\n", RCC->APB1ENR);
printf("Debug: RCC->APB2ENR = 0x%08lX\r\n", RCC->APB2ENR);
printf("Debug: NVIC pending interrupts: 0x%08lX\r\n", NVIC->ISPR[0]);
```

**期望值**:
- `RCC->AHBENR`: 应该只有必要的时钟启用
- `RCC->APB1ENR`: 应该接近0x00000000
- `RCC->APB2ENR`: 应该接近0x00000000
- `NVIC->ISPR[0]`: 应该是0x00000000（无挂起中断）

## 🎯 预期效果

修改后应该能够实现：
- **休眠功耗**: < 10μA
- **唤醒时间**: < 10μs
- **正常工作**: 所有功能在唤醒后正常恢复

## 🔍 进一步排查步骤

如果问题仍然存在：

1. **检查硬件**:
   - 确认外部模块电源完全断开
   - 检查是否有上拉电阻导致漏电流
   - 测量各个电源域的电流

2. **检查调试输出**:
   - 观察寄存器值是否符合预期
   - 确认没有挂起的中断

3. **逐步排除**:
   - 先注释掉所有外设初始化
   - 逐个添加外设，找出问题源

4. **使用STM32CubeMX**:
   - 检查低功耗配置
   - 确认所有不需要的外设都已禁用

## 🔌 STOP模式下的中断机制

### 可以唤醒STOP模式的中断源：
- **EXTI外部中断** ✅ (PB0 - MLX90393 INT)
- **RTC中断** ✅ (如果启用)
- **IWDG中断** ✅ (如果启用)
- **某些低功耗定时器** ✅

### 不能唤醒STOP模式的中断源：
- **UART中断** ❌ (除非是LPUART在特定配置下)
- **ADC中断** ❌
- **DMA中断** ❌
- **I2C中断** ❌
- **SysTick中断** ❌

### 关键原则：
```c
// ✅ 正确：保持唤醒中断启用
HAL_NVIC_EnableIRQ(EXTI0_1_IRQn);  // PB0外部中断

// ❌ 错误：禁用唤醒中断会导致无法唤醒
HAL_NVIC_DisableIRQ(EXTI0_1_IRQn); // 这会导致无法唤醒！

// ✅ 正确：禁用非唤醒中断以降低功耗
HAL_NVIC_DisableIRQ(USART2_IRQn);
HAL_NVIC_DisableIRQ(DMA1_Channel1_IRQn);
```

## ⚠️ 注意事项

1. **时钟配置**: 你已经正确修改为RCC_MSIRANGE_5 (2.097MHz)
2. **UART时钟源**: 已改为SYSCLK，这是正确的
3. **外部模块**: 确保RF_PWR_OFF宏定义正确
4. **GPIO状态**: 确保所有输出引脚在休眠前处于正确状态
5. **唤醒中断**: 绝对不要禁用EXTI0_1_IRQn中断！

---

**测试建议**: 运行修改后的代码，观察调试输出的寄存器值，这将帮助确定具体的问题所在。
