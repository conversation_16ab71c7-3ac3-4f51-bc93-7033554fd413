# E70 LoRa模块使用指南 - 实际验证版本

## 🔥 重要发现（已实际验证）

**E70模块必须在上电前设置好模式引脚！**

E70模块在启动时读取M0、M1、M2引脚状态来确定工作模式，而不是在运行时动态切换。

## ✅ 实际验证的工作状态

**测试日期：2025年1月**
**测试结果：完全成功**

### 验证的模式：
- ❌ **配置模式3 (011)**: 发送命令无响应，不可用
- ✅ **配置模式5 (101)**: 完全正常，读写配置成功
- ✅ **连续通信模式 (001)**: 用于正常数据传输

### 验证的命令：
- ✅ **C1 C1 C1**: 读取工作参数 → 返回6字节配置数据
- ❌ **C3 C3 C3**: 读取版本信息 → 此型号不支持
- ❌ **C4 C4 C4**: 复位命令 → 此型号不支持
- ✅ **C0 配置数据**: 写入配置 → 立即生效并可验证

## 硬件连接

### 引脚连接
```
STM32 PA2 (LPUART1_TX) -> E70 RXD
STM32 PA3 (LPUART1_RX) -> E70 TXD
STM32 PA4 -> E70 M0
STM32 PA5 -> E70 M1
STM32 PA6 -> E70 M2
STM32 PB1 -> E70 VCC (电源控制)
GND -> GND
```

### 工作模式
```
M2 M1 M0 | 模式
---------|----------
0  <USER>  <GROUP>  | 禁用模式 (禁用所有通信) ❌
0  0  1  | 连续通信模式 (正常通信) ✅
0  1  0  | 省电模式
0  1  1  | 配置模式3 (实测不工作) ❌
1  0  0  | RSSI模式
1  0  1  | 配置模式5 (实测工作) ✅
1  1  0  | 保留模式
1  1  1  | 深度休眠模式
```

## 核心函数

### 1. 初始化
```c
void E70_Init(void);
```
- 初始化GPIO引脚
- 设置为透明传输模式
- 必须在使用其他功能前调用

### 2. 设置地址
```c
HAL_StatusTypeDef E70_SetAddress(uint16_t address);
```
- 设置模块地址 (0x0000-0xFFFF)
- 自动切换到配置模式，设置完成后返回透明模式
- 示例：`E70_SetAddress(0x1234);`

### 3. 设置信道
```c
HAL_StatusTypeDef E70_SetChannel(uint8_t channel);
```
- 设置通信信道 (0-31)
- 自动切换到配置模式，设置完成后返回透明模式
- 示例：`E70_SetChannel(5);`

### 4. 设置发射功率
```c
HAL_StatusTypeDef E70_SetTxPower(E70_Power_t power);
```
- 设置发射功率
- 可选值：
  - `E70_POWER_14DBM` (14dBm，默认)
  - `E70_POWER_10DBM` (10dBm)
  - `E70_POWER_7DBM` (7dBm)
  - `E70_POWER_4DBM` (4dBm)
- 示例：`E70_SetTxPower(E70_POWER_10DBM);`

### 5. 获取版本信息
```c
HAL_StatusTypeDef E70_GetVersion(uint8_t *version, uint8_t len);
```
- 获取模块版本信息
- 示例：
```c
uint8_t version[8];
E70_GetVersion(version, sizeof(version));
```

## 使用示例

### 基本配置
```c
int main(void)
{
    // 系统初始化...

    // 初始化E70模块
    E70_Init();

    // 配置模块
    E70_SetAddress(0x1234);      // 设置地址
    E70_SetChannel(5);           // 设置信道5
    E70_SetTxPower(E70_POWER_10DBM); // 设置10dBm功率

    // 获取版本信息
    uint8_t version[8];
    E70_GetVersion(version, sizeof(version));

    // 现在可以进行透明传输通信
    while(1)
    {
        // 发送数据
        uint8_t data[] = "Hello E70";
        HAL_UART_Transmit(&hlpuart1, data, sizeof(data)-1, 1000);

        // 接收数据
        uint8_t rx_buffer[100];
        HAL_UART_Receive(&hlpuart1, rx_buffer, sizeof(rx_buffer), 1000);

        HAL_Delay(1000);
    }
}
```

## 关键技术要点

### 1. 正确的模式切换方法
```c
void E70_SetModeWithPowerCycle(E70_Mode_t mode)
{
    // 1. 断电
    RF_PWR_OFF;
    HAL_Delay(1000);

    // 2. 设置模式引脚
    HAL_GPIO_WritePin(E70_M0_GPIO_Port, E70_M0_Pin, (mode & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(E70_M1_GPIO_Port, E70_M1_Pin, (mode & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(E70_M2_GPIO_Port, E70_M2_Pin, (mode & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET);

    // 3. 上电
    RF_PWR_ON;
    HAL_Delay(2000);  // 等待启动完成
}
```

### 2. 配置命令格式
- **读取配置**: `C1 C1 C1` -> 返回 `C0 ADDH ADDL SPED CHAN OPTION`
- **读取版本**: `C3 C3 C3` -> 返回版本信息
- **写入配置**: `C0 ADDH ADDL SPED CHAN OPTION` -> 保存到flash
- **临时配置**: `C2 ADDH ADDL SPED CHAN OPTION` -> 临时设置

### 3. 配置参数说明
- **ADDH/ADDL**: 模块地址高低字节
- **SPED**: 串口波特率和空中数据率
- **CHAN**: 通信信道 (0-31)
- **OPTION**: 各种选项设置，包括发射功率

## 常见问题

### Q: 为什么配置命令不响应？
A: 确保使用 `E70_SetModeWithPowerCycle()` 切换到配置模式，而不是 `E70_SetMode()`。

### Q: 如何确认模块工作正常？
A: 调用 `E70_GetVersion()` 获取版本信息，如果成功返回则说明通信正常。

### Q: 透明传输模式下如何通信？
A: 直接使用UART发送和接收数据，E70会自动转发到无线信道。

## 注意事项

1. **必须使用正确的上电时序** - 先设置引脚，后上电
2. **配置操作会自动切换模式** - 无需手动切换
3. **所有配置都保存到flash** - 断电后配置保持
4. **串口波特率固定9600** - 不要修改SPED参数
5. **信道范围0-31** - 超出范围会返回错误

## 文件结构

- `e70_config.h` - 头文件，包含所有定义和函数声明
- `e70_config_clean.c` - 源文件，包含所有功能实现
- `E70_Usage_Guide.md` - 本使用指南

## 版本历史

- v1.0 - 初始版本，包含基本功能
- v2.0 - 发现并解决上电时序问题，大幅提高可靠性
