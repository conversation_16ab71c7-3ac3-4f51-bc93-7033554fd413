# E70 故障排除与代码恢复指南

## 🚨 如果代码不工作了，按此步骤恢复

### 1. 检查关键配置

#### GPIO 配置（CubeMX生成，不要手动修改）
```c
// main.h 中应该有这些定义：
#define M0_Pin GPIO_PIN_4
#define M0_GPIO_Port GPIOA
#define M1_Pin GPIO_PIN_5
#define M1_GPIO_Port GPIOA
#define M2_Pin GPIO_PIN_6
#define M2_GPIO_Port GPIOA
#define RF_PW_Pin GPIO_PIN_1
#define RF_PW_GPIO_Port GPIOB
```

#### UART 配置（必须是9600波特率）
```c
// usart.c 中 LPUART1 配置：
hlpuart1.Init.BaudRate = 9600;
hlpuart1.Init.WordLength = UART_WORDLENGTH_8B;
hlpuart1.Init.StopBits = UART_STOPBITS_1;
hlpuart1.Init.Parity = UART_PARITY_NONE;
hlpuart1.Init.Mode = UART_MODE_TX_RX;
```

### 2. 关键代码片段（必须正确）

#### 模式定义（e70_config.h）
```c
typedef enum {
    E70_MODE_DISABLE = 0,       // 禁用模式 (000) - 禁用所有通信 ❌
    E70_MODE_TRANS = 1,         // 连续通信模式 (001) - 正常通信模式 ✅
    E70_MODE_POWER_SAVE = 2,    // 省电模式 (010)
    E70_MODE_CONFIG3 = 3,       // 配置模式3 (011) - 实测无响应，不可用 ❌
    E70_MODE_RSSI = 4,          // RSSI模式 (100)
    E70_MODE_CONFIG5 = 5,       // 配置模式5 (101) - 实测有效的配置模式 ✅
    E70_MODE_RESERVED = 6,      // 保留模式 (110)
    E70_MODE_DEEP_SLEEP = 7     // 深度休眠模式 (111)
} E70_Mode_t;
```

#### 模式切换函数（e70_config.c）
```c
void E70_SetModeWithPowerCycle(E70_Mode_t mode)
{
    printf("Setting E70 mode %d (M2M1M0=%d%d%d)\r\n", mode,
           (mode & 0x04) ? 1 : 0, (mode & 0x02) ? 1 : 0, (mode & 0x01) ? 1 : 0);

    // 1. 断电
    RF_PWR_OFF;
    HAL_Delay(1000);

    // 2. 设置模式引脚
    HAL_GPIO_WritePin(M0_GPIO_Port, M0_Pin, (mode & 0x01) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(M1_GPIO_Port, M1_Pin, (mode & 0x02) ? GPIO_PIN_SET : GPIO_PIN_RESET);
    HAL_GPIO_WritePin(M2_GPIO_Port, M2_Pin, (mode & 0x04) ? GPIO_PIN_SET : GPIO_PIN_RESET);

    HAL_Delay(100);  // 让引脚状态稳定

    // 3. 上电
    RF_PWR_ON;
    HAL_Delay(3000);  // 等待启动完成

    printf("E70 powered on in mode %d\r\n", mode);
}
```

#### 中断接收函数（e70_config.c）
```c
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == LPUART1) {
        // 数据已经接收到 uart_rx_buffer[uart_rx_index]
        uart_rx_index++;

        // 继续接收下一个字节，不做任何打印和判断
        if (uart_rx_index < sizeof(uart_rx_buffer)) {
            HAL_UART_Receive_IT(&hlpuart1, (uint8_t*)&uart_rx_buffer[uart_rx_index], 1);
        }
    }
}
```

### 3. 基本测试代码（main.c）
```c
int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_I2C1_Init();
    MX_LPUART1_UART_Init();
    MX_USART2_UART_Init();
    MX_ADC_Init();

    printf("=== E70 Module Test Start ===\r\n");

    // 进入配置模式5 (101) - 这是有效的配置模式
    E70_SetModeWithPowerCycle(E70_MODE_CONFIG5);
    E70_Init();

    // 测试：读取工作参数
    printf("Reading work parameters (C1 C1 C1)\r\n");
    E70_StartRx();
    uint8_t cmd_cfg[] = {0xC1, 0xC1, 0xC1};
    HAL_UART_Transmit(&hlpuart1, cmd_cfg, 3, 1000);
    HAL_Delay(1000);
    E70_StopRx();
    E70_PrintRxBuffer();
    E70_ParseConfigData();

    while(1)
    {
        LED_TOGGLE;
        HAL_Delay(200);
    }
}
```

### 4. 常见问题与解决方案

#### 问题1：没有接收到数据
**检查项目：**
- 确认使用配置模式5 (101)，不要使用模式3 (011)
- 确认UART波特率是9600
- 确认硬件连接正确
- 确认电源控制引脚工作正常

#### 问题2：编译错误
**检查项目：**
- 确认 e70_config.h 中没有重复的GPIO定义
- 确认使用 M0_Pin 而不是 E70_M0_Pin
- 确认包含了正确的头文件

#### 问题3：中断不工作
**检查项目：**
- 确认 HAL_UART_RxCpltCallback 函数存在
- 确认中断回调中没有printf语句
- 确认 E70_StartRx() 被正确调用

### 5. 预期的正确输出
```
=== E70 Module Test Start ===
Setting E70 mode 5 (M2M1M0=101)
E70 powered on in mode 5
E70 init: GPIO already configured by CubeMX, no extra init.
Reading work parameters (C1 C1 C1)
=== RX Buffer Dump ===
Total bytes received: 6
Data: 0xC0 0x00 0x00 0x18 0x4E 0x1C
======================
=== Config Data Analysis ===
Header: 0xC0
Address: 0x0000
SPED: 0x18 (Baud: 9600, Air: 2.5k)
Channel: 14
Option: 0x1C (Power: 14dBm)
============================
```

### 6. 紧急恢复步骤
如果代码完全混乱，按以下顺序恢复：

1. **重新生成CubeMX项目**（保留用户代码）
2. **复制关键文件**：
   - `Inc/e70_config.h`
   - `Src/e70_config.c`
3. **修改main.c**：添加基本测试代码
4. **编译测试**：确认能接收到6字节配置数据

### 7. 验证标准
代码正确时应该能够：
- ✅ 读取配置参数 (C1 C1 C1) → 6字节数据
- ✅ 写入新配置并验证生效
- ✅ 模式切换正常工作
- ✅ 数据解析显示正确参数

---
**最后更新：2025年1月**
**状态：完全验证可用**
