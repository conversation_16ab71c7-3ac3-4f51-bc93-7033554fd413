
根据MLX90393数据手册（Revision 007），以下是寄存器配置相关信息的提炼，便于后续代码开发使用：
---
### **一、寄存器地址映射**
#### **客户可访问区域（Volatile/NV Memory）**
| 地址范围 | 用途                  | 说明                                                                 |
|----------|-----------------------|----------------------------------------------------------------------|
| 0x00-0x09 | **配置寄存器**        | 存储传感器工作参数（增益、滤波、模式等）                             |
| 0x0A-0x1F | **用户自由区**        | 可存储自定义数据（352位），需通过`HS`命令写入非易失存储              |
---
### **二、关键配置寄存器详解**
#### **1. 基础配置寄存器（0x00）**
| 位域          | 名称             | 说明                                                                 | 默认值 |
|---------------|------------------|----------------------------------------------------------------------|--------|
| `[15:12]`     | `Z_SERIES`       | Z轴测量使能（1=启用串联霍尔板）                                      | 0      |
| `[11:9]`      | `GAIN_SEL[2:0]`  | **模拟增益选择**（0-7，增益范围5倍）                                 | 0x7    |
| `[8:5]`       | `HALLCONF[3:0]`  | 霍尔板旋转频率（影响最小采样率）                                     | 0xC    |
| `[4:0]`       | `ANA_RESERVED_LOW` | 保留位（不可修改）                                                   | -      |
#### **2. 模式控制寄存器（0x01）**
| 位域          | 名称                | 说明                                                                 | 默认值 |
|---------------|---------------------|----------------------------------------------------------------------|--------|
| `[15:12]`     | `BURST_SEL[3:0]`    | **突发模式测量轴**（ZYXT位映射，如0xF=XYZT全测）                     | 0xF    |
| `[11:5]`      | `BURST_DATA_RATE`  | 突发模式间隔（`T_INTERVAL = BURST_DATA_RATE × 20ms`）               | 0      |
| `[4]`         | `TRIG_INT_SEL`     | 引脚模式（0=TRIG触发，1=INT中断）                                    | 1      |
| `[3:2]`       | `COMM_MODE[1:0]`   | **通信协议**（00=自动，10=仅SPI，11=仅I²C）                          | 0      |
| `[1]`         | `WOC_DIFF`         | 唤醒模式（0=与初始值比，1=与上次值比）                               | 0      |
| `[0]`         | `EXT_TRIG`         | 外部触发使能（需`TRIG_INT_SEL=0`）                                   | 0      |
#### **3. 分辨率与滤波寄存器（0x02）**
| 位域          | 名称             | 说明                                                                 | 默认值 |
|---------------|------------------|----------------------------------------------------------------------|--------|
| `[15:10]`     | `RES_Y`          | Y轴分辨率选择（与`GAIN_SEL`联合决定灵敏度）                          | 0      |
| `[9:4]`       | `RES_X`          | X轴分辨率选择                                                        | 0      |
| `[3:2]`       | `DIG_FILT[1:0]`  | **数字滤波器**（0-7，影响噪声与转换时间）                            | 0      |
| `[1:0]`       | `OSR[1:0]`       | 磁场ADC过采样率（0-3）                                               | 0      |
| `[7:6]`       | `OSR2[1:0]`      | 温度ADC过采样率（0-3）                                               | 0      |
| `[5:0]`       | `RES_Z`          | Z轴分辨率选择                                                        | 0      |
#### **4. 温度补偿寄存器（0x03）**
| 位域          | 名称             | 说明                                                                 | 默认值 |
|---------------|------------------|----------------------------------------------------------------------|--------|
| `[15:8]`      | `SENS_TC_LT[7:0]` | 低温补偿系数（T < T_REF）                                            | 0      |
| `[7:0]`       | `SENS_TC_HT[7:0]` | 高温补偿系数（T > T_REF）                                            | 0      |
#### **5. 偏移校准寄存器（0x04-0x06）**
| 地址   | 名称         | 说明                     | 默认值 |
|--------|--------------|--------------------------|--------|
| 0x04   | `OFFSET_X`   | X轴偏移校准（16位有符号）| 0      |
| 0x05   | `OFFSET_Y`   | Y轴偏移校准              | 0      |
| 0x06   | `OFFSET_Z`   | Z轴偏移校准              | 0      |
#### **6. 唤醒阈值寄存器（0x07-0x09）**
| 地址   | 名称               | 说明                     | 默认值 |
|--------|--------------------|--------------------------|--------|
| 0x07   | `WOXY_THRESHOLD`   | XY轴唤醒阈值（16位）     | 0      |
| 0x08   | `WOZ_THRESHOLD`    | Z轴唤醒阈值              | 0      |
| 0x09   | `WOT_THRESHOLD`    | 温度唤醒阈值             | 0      |
---
### **三、关键配置关系**
#### **1. 灵敏度计算（HALLCONF=0xC时）**
| `GAIN_SEL` | `RES_XYZ` | X/Y轴灵敏度 (µT/LSB) | Z轴灵敏度 (µT/LSB) |
|------------|-----------|----------------------|-------------------|
| 0          | 0         | 0.751                | 1.210             |
| 0          | 1         | 1.502                | 2.420             |
| ...        | ...       | ...                  | ...               |
| 7          | 3         | 1.202                | 1.936             |
> **注**：启用温度补偿（`TCMP_EN=1`）时，数据格式变为无符号数（0mT = 32768 LSB）。
#### **2. 转换时间与滤波**
- **磁场转换时间**：`T_CONV = 67 + 64 × 2^OSR × (2 + 2^DIG_FILT)` µs
- **温度转换时间**：`T_CONV_T = 67 + 192 × 2^OSR2` µs
- **最大输出数据率**：受`OSR`和`DIG_FILT`限制（详见表20）。
---
### **四、配置操作流程**
#### **1. 寄存器读写**
| 命令 | 操作       | 说明                                                                 |
|------|------------|----------------------------------------------------------------------|
| `WR` | 写寄存器   | 地址需左移2位（如写0x00 → 命令参数为`0x00 << 2 = 0x00`）             |
| `RR` | 读寄存器   | 同上，返回16位数据                                                   |
| `HS` | 存储到NVM  | **需等待15ms**，且VDD≥3.3V                                           |
| `HR` | 恢复NVM    | 上电自动执行                                                         |
#### **2. 模式切换**
| 命令 | 模式               | 参数说明                     |
|------|--------------------|------------------------------|
| `SB` | 突发模式（Burst）  | 参数`zyxt`指定测量轴（如0x3=XYZ） |
| `SW` | 唤醒模式（WOC）    | 同上                          |
| `SM` | 单次测量模式       | 同上                          |
| `EX` | 退出当前模式       | -                            |
---
### **五、代码开发注意事项**
1. **通信协议选择**：
   - 通过`COMM_MODE[1:0]`强制SPI/I²C，或依赖`SENB/CS`引脚（低=SPI，高=I²C）。
2. **地址设置**：
   - I²C地址由`A0/A1`引脚和订购码决定（如ABA-011 → 地址范围`0x0C-0x0F`）。
3. **低功耗优化**：
   - 通过`BURST_DATA_RATE`控制采样间隔（0=连续采样，>0=间歇采样）。
4. **错误处理**：
   - 读取状态字节检查`ERROR`位（命令无效/内存错误）和`SED`位（单比特错误已修正）。
5. **温度补偿**：
   - 启用`TCMP_EN`后，磁场数据格式变为无符号数，需转换公式：`Field = (Data - 32768) × Sensitivity`。
---
### **六、寄存器配置示例（伪代码）**
```c
// 配置突发模式：测量XYZ轴，增益=7，分辨率=0
uint8_t config_burst[4] = {
    0x60,                   // WR命令
    0x00 << 2,              // 寄存器0x00
    (0 << 12) | (7 << 9) | (0xC << 5), // GAIN_SEL=7, HALLCONF=0xC
    0x00                    // 低字节
};
spi_write(config_burst, 4);
// 启动突发模式（测量XYZ）
uint8_t start_burst[2] = {0x10, 0x07}; // SB命令 + zyxt=0111 (XYZ)
spi_write(start_burst, 2);
```
> **完整寄存器位域定义**详见手册第16.2节，**时序参数**参考第12章，**电气特性**参考第10-11章。
