# STM32L031G6 STOP模式低功耗唤醒功能说明

## 🎯 功能概述

本项目实现了基于STM32L031G6U6单片机的超低功耗系统，具有以下特点：
- **STOP模式休眠**：系统自动进入STOP模式，功耗降至微安级别
- **外部中断唤醒**：仅通过MLX90393磁场传感器的INT信号（PB0）唤醒
- **完整工作流程**：唤醒后执行ADC采样、LED指示、传感器初始化等任务
- **自动循环**：工作完成后自动重新进入休眠状态

## 🔧 硬件配置

### 关键引脚配置
- **PB0**: MLX90393 INT输出 → STM32 外部中断输入（上升沿触发）
- **PA8**: MLX90393_PW_Pin → MLX90393电源控制
- **PC14**: LED_Pin → 状态指示LED
- **PA0**: ADC_IN0 → 电池电压采样（通过2:1分压）

### 时钟配置
- **系统时钟**: MSI 65.536 kHz（超低功耗）
- **UART时钟**: HSI 16 MHz（保证115200波特率）
- **外设时钟**: 休眠时自动关闭，唤醒时重新启用

## 🚀 工作流程

### 1. 系统启动
```c
printf("=== STM32L031G6 Low Power System ===\r\n");
printf("System will enter STOP mode after initialization\r\n");
printf("Wake up by MLX90393 INT signal on PB0\r\n");
```

### 2. 主循环工作流程
每次唤醒后执行以下步骤：

#### 步骤1：MLX90393断电复位
```c
MLX90393_PW_OFF;
printf("MLX90393 Power: OFF (3 seconds for complete reset)\r\n");
HAL_Delay(3000);  // 必须断电3秒以上才能完全复位
```

#### 步骤2：ADC电压采样
```c
uint16_t battery_voltage_mv = ADC_ReadBatteryVoltage();
float battery_voltage_v = battery_voltage_mv / 1000.0f;
printf("Battery Voltage: %.3fV (%dmV)\r\n", battery_voltage_v, battery_voltage_mv);
```

#### 步骤3：LED状态指示
```c
printf("LED blinking 3 times...\r\n");
for(int i = 0; i < 3; i++) {
    LED_TOGGLE;
    HAL_Delay(200);
    LED_TOGGLE;
    HAL_Delay(200);
}
```

#### 步骤4：MLX90393重新初始化
```c
MLX90393_PW_ON;
HAL_Delay(100);

if (MLX90393_Init(&mlx_handle, &hi2c1) == MLX90393_OK) {
    if (MLX90393_ConfigureWOC(&mlx_handle) == MLX90393_OK) {
        printf("MLX90393 WOC Mode: ENABLED\r\n");
        printf("INT pin (PB0) ready for interrupts\r\n");
    }
}
```

#### 步骤5：进入STOP模式
```c
// 关闭外设时钟
__HAL_RCC_USART2_CLK_DISABLE();
__HAL_RCC_LPUART1_CLK_DISABLE();
__HAL_RCC_ADC1_CLK_DISABLE();
__HAL_RCC_DMA1_CLK_DISABLE();

// 进入STOP模式
HAL_SuspendTick();
HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
```

#### 步骤6：唤醒后恢复
```c
// 重新配置系统时钟
SystemClock_Config();
HAL_ResumeTick();

// 重新启用外设时钟
__HAL_RCC_USART2_CLK_ENABLE();
__HAL_RCC_LPUART1_CLK_ENABLE();
__HAL_RCC_ADC1_CLK_ENABLE();
__HAL_RCC_DMA1_CLK_ENABLE();

// 重新初始化UART
MX_USART2_UART_Init();
MX_LPUART1_UART_Init();
```

## 🔌 中断处理

### 外部中断回调函数
```c
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    if (GPIO_Pin == GPIO_PIN_0) // PB0 - MLX90393 INT引脚
    {
        // MLX90393中断触发，设备从STOP模式唤醒
        LED_TOGGLE;  // 简单的唤醒指示
        // 主循环会处理完整的工作流程
    }
}
```

## ⚡ 功耗特性

### STOP模式功耗
- **CPU**: 停止运行
- **RAM**: 内容保持
- **外设**: 大部分关闭
- **唤醒源**: 仅外部中断（PB0）
- **预期功耗**: < 10μA

### 运行模式功耗
- **系统时钟**: 65.536 kHz（极低功耗）
- **工作时间**: 约5-6秒（包含3秒MLX90393断电时间）
- **平均功耗**: 取决于唤醒频率

## 🛠️ 关键设计要点

### 1. MLX90393电源管理
- **断电时间**: 必须≥3秒才能完全复位
- **上电顺序**: 断电 → 等待3秒 → 上电 → 初始化 → 配置WOC模式
- **中断特性**: 一旦触发中断，必须断电重启才能恢复

### 2. 时钟配置优化
- **系统时钟**: 使用MSI最低频率降低运行功耗
- **UART时钟**: 使用HSI保证通信质量
- **动态管理**: 休眠时关闭，唤醒时重新启用

### 3. 外设时钟管理
```c
// 休眠前关闭
__HAL_RCC_USART2_CLK_DISABLE();
__HAL_RCC_LPUART1_CLK_DISABLE();
__HAL_RCC_ADC1_CLK_DISABLE();
__HAL_RCC_DMA1_CLK_DISABLE();

// 唤醒后重新启用
__HAL_RCC_USART2_CLK_ENABLE();
__HAL_RCC_LPUART1_CLK_ENABLE();
__HAL_RCC_ADC1_CLK_ENABLE();
__HAL_RCC_DMA1_CLK_ENABLE();
```

## 📊 性能指标

### 时序特性
- **唤醒时间**: < 10μs（STOP模式唤醒）
- **工作周期**: 约6秒（含3秒断电等待）
- **休眠时间**: 无限期（直到外部触发）

### 电源效率
- **休眠功耗**: < 10μA
- **工作功耗**: 约2-5mA（取决于外设使用）
- **平均功耗**: 主要取决于唤醒频率

## 🔍 调试信息

系统运行时会输出详细的调试信息：
```
=== Device Wake Up ===
MLX90393 Power: OFF (3 seconds for complete reset)
Waiting 3 seconds for MLX90393 reset and ADC stabilization...
Battery Voltage: 4.150V (4150mV)
LED blinking 3 times...
Initializing MLX90393...
MLX90393 Init: SUCCESS
MLX90393 WOC Mode: ENABLED
INT pin (PB0) ready for interrupts
Preparing to enter STOP mode...
Device will wake up on MLX90393 interrupt (PB0)

*** WAKE UP from STOP mode ***
```

## 📝 使用说明

1. **编译下载**: 将代码编译并下载到STM32L031G6U6
2. **硬件连接**: 确保MLX90393的INT引脚连接到PB0
3. **电源管理**: 确保MLX90393_PW_Pin（PA8）能够控制传感器电源
4. **测试触发**: 使用磁铁靠近MLX90393传感器触发中断
5. **观察输出**: 通过串口观察系统工作状态

## ⚠️ 注意事项

1. **断电时间**: MLX90393断电时间必须≥3秒
2. **中断配置**: 确保PB0配置为上升沿触发的外部中断
3. **时钟恢复**: STOP模式唤醒后必须重新配置系统时钟
4. **外设初始化**: 唤醒后需要重新初始化UART等外设
5. **电源管理**: 确保MLX90393电源控制电路工作正常

---

**版本**: v1.0  
**更新日期**: 2025年1月  
**状态**: 已实现并测试
