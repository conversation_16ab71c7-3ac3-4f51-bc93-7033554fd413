# MLX90393 参数配置指南

## 📍 配置文件位置

所有MLX90393的关键参数都在 **`Inc/mlx90393.h`** 文件中定义，你可以直接修改这些宏定义来调整参数。

## 🔧 关键参数配置

### 1. **增益设置**
```c
// 在mlx90393.c的MLX90393_EnterLowPowerMode函数中
reg_data |= (0 << 9);   // GAIN_SEL = 0 (最低增益)

// 可选值：
// 0 = 最低增益 (低功耗，低灵敏度)
// 1-7 = 逐渐增加增益 (高功耗，高灵敏度)
```

### 2. **数字滤波设置**
```c
// 在mlx90393.h文件中 (第48行)
#define MLX90393_DIG_FILT           0       // 数字滤波

// 可选值：
// 0 = 无滤波 (最快响应，可能有噪声)
// 1-7 = 逐渐增加滤波 (响应变慢，噪声减少)
```

### 3. **触发阈值设置**
```c
// 在mlx90393.h文件中 (第61-62行)
#define MLX90393_WOC_XY_THRESHOLD   100     // X/Y轴变化阈值
#define MLX90393_WOC_Z_THRESHOLD    100     // Z轴变化阈值

// 阈值说明：
// 50-100   = 高灵敏度 (容易触发)
// 200-300  = 中等灵敏度
// 400-500  = 低灵敏度 (不容易触发)
```

### 4. **灵敏度预设**
```c
// 在mlx90393.h文件中 (第79-81行)
#define MLX90393_WOC_SENSITIVITY_HIGH    // 当前启用
//#define MLX90393_WOC_SENSITIVITY_MEDIUM  // 注释掉
//#define MLX90393_WOC_SENSITIVITY_LOW     // 注释掉

// 预设值：
// HIGH:   阈值100 (高灵敏度)
// MEDIUM: 阈值200 (中等灵敏度)  
// LOW:    阈值500 (低灵敏度)
```

### 5. **监测轴选择**
```c
// 在mlx90393.h文件中 (第76行)
#define MLX90393_WOC_AXES  (MLX90393_WOC_AXIS_Y | MLX90393_WOC_AXIS_Z | MLX90393_WOC_AXIS_X)

// 可选组合：
// MLX90393_WOC_AXIS_X                                    // 只监测X轴
// MLX90393_WOC_AXIS_Y                                    // 只监测Y轴
// MLX90393_WOC_AXIS_Z                                    // 只监测Z轴
// (MLX90393_WOC_AXIS_X | MLX90393_WOC_AXIS_Y)           // X+Y轴
// (MLX90393_WOC_AXIS_Y | MLX90393_WOC_AXIS_Z)           // Y+Z轴
// (MLX90393_WOC_AXIS_X | MLX90393_WOC_AXIS_Z)           // X+Z轴
// (MLX90393_WOC_AXIS_X | MLX90393_WOC_AXIS_Y | MLX90393_WOC_AXIS_Z)  // 三轴
```

## 🎯 参数调整建议

### 提高灵敏度（更容易触发）
```c
// 1. 降低阈值
#define MLX90393_WOC_XY_THRESHOLD   50      // 从100改为50
#define MLX90393_WOC_Z_THRESHOLD    50      // 从100改为50

// 2. 增加增益（在代码中修改）
reg_data |= (2 << 9);   // GAIN_SEL = 2 (增加增益)

// 3. 减少数字滤波
#define MLX90393_DIG_FILT           0       // 保持0
```

### 降低灵敏度（减少误触发）
```c
// 1. 提高阈值
#define MLX90393_WOC_XY_THRESHOLD   300     // 从100改为300
#define MLX90393_WOC_Z_THRESHOLD    300     // 从100改为300

// 2. 降低增益（在代码中修改）
reg_data |= (0 << 9);   // GAIN_SEL = 0 (保持最低)

// 3. 增加数字滤波
#define MLX90393_DIG_FILT           3       // 从0改为3
```

### 优化功耗
```c
// 1. 最低增益
reg_data |= (0 << 9);   // GAIN_SEL = 0

// 2. 最低分辨率
reg_data |= (3 << 14);  // RES_Z = 3
reg_data |= (3 << 10);  // RES_Y = 3  
reg_data |= (3 << 4);   // RES_X = 3

// 3. 最少滤波
#define MLX90393_DIG_FILT           0

// 4. 间歇采样
reg_data |= (50 << 5);  // BURST_DATA_RATE = 50 (1秒间隔)
```

## 🧪 测试步骤

### 1. **修改参数**
在`Inc/mlx90393.h`中修改相应的宏定义

### 2. **重新编译**
修改头文件后需要重新编译整个项目

### 3. **测试触发**
- 用磁铁测试不同距离的触发效果
- 观察串口输出的磁场数据
- 调整阈值直到满意

### 4. **功耗测试**
- 测量休眠电流是否仍在300μA左右
- 如果功耗增加，适当降低增益或分辨率

## 📊 参数对比表

| 参数 | 低功耗设置 | 平衡设置 | 高性能设置 |
|------|------------|----------|------------|
| GAIN_SEL | 0 | 2 | 5 |
| DIG_FILT | 0 | 2 | 5 |
| WOC_THRESHOLD | 200-300 | 100-150 | 50-80 |
| RES_XYZ | 3 | 1 | 0 |
| 功耗 | <100μA | ~200μA | >500μA |
| 灵敏度 | 低 | 中 | 高 |

## 🔍 调试技巧

### 1. **查看实际磁场值**
```c
// 在中断处理中查看数据
printf("MLX90393 Data: X=%.1f, Y=%.1f, Z=%.1f\r\n", 
       interrupt_data.x, interrupt_data.y, interrupt_data.z);
```

### 2. **计算变化量**
```c
// 手动计算磁场变化量，确认阈值设置
float delta_x = fabs(current_x - previous_x);
float delta_y = fabs(current_y - previous_y);
float delta_z = fabs(current_z - previous_z);
printf("Delta: X=%.1f, Y=%.1f, Z=%.1f\r\n", delta_x, delta_y, delta_z);
```

### 3. **单轴测试**
```c
// 先测试单个轴，找出最敏感的轴
#define MLX90393_WOC_AXES  MLX90393_WOC_AXIS_Z  // 只测试Z轴
```

## ⚠️ 注意事项

1. **修改头文件后必须重新编译**
2. **增益和阈值需要配合调整**
3. **高增益会增加功耗**
4. **过低的阈值可能导致误触发**
5. **建议从保守设置开始，逐步调整**

## 🎯 推荐的调试顺序

1. **先确保基本功能正常**（当前状态）
2. **测试单轴触发**（找出最敏感轴）
3. **调整阈值**（从高到低逐步降低）
4. **优化增益**（在功耗和性能间平衡）
5. **最终测试**（长期稳定性验证）

---

**所有参数都在mlx90393.h文件中，修改后重新编译即可测试！**
