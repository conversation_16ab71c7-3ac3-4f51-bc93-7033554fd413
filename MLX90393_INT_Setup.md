# MLX90393 中断功能配置说明

## 硬件连接
- MLX90393 INT 引脚 → STM32L031G6 PB0 引脚
- PB0 配置为上升沿中断输入模式

## 代码修改总结

### 1. GPIO配置 (Src/gpio.c)
- PB0 配置为 `GPIO_MODE_IT_RISING` (上升沿中断)
- 启用 NVIC 中断: `EXTI0_1_IRQn`

### 2. 中断处理 (Src/stm32l0xx_it.c)
- 添加 `EXTI0_1_IRQHandler()` 中断服务程序
- 添加 `HAL_GPIO_EXTI_Callback()` 回调函数
- 中断触发时打印消息并切换LED状态

### 3. 主程序 (Src/main.c)
- 初始化MLX90393传感器
- 配置WOC (Wake-up On Change) 模式
- 启动WOC模式监测X、Y、Z三轴磁场变化
- 在主循环中显示传感器状态

### 4. MLX90393驱动 (Src/mlx90393.c)
- 完善 `MLX90393_ConfigureWOC()` 函数
- 配置中断模式和变化检测阈值
- 启动WOC模式并验证状态

## 功能说明

### WOC模式配置
- **监测轴**: X、Y、Z三轴
- **XY轴阈值**: 80 LSB
- **Z轴阈值**: 100 LSB
- **中断模式**: 上升沿触发

### 中断触发条件
当磁场变化超过设定阈值时，MLX90393会拉高INT引脚，触发STM32的外部中断。

### 测试方法
1. 编译并下载程序到STM32L031G6
2. 打开串口监视器 (波特率根据UART配置)
3. 观察初始化信息
4. 用磁铁靠近或远离MLX90393传感器
5. 观察串口输出的中断触发消息
6. LED会在中断触发时切换状态

## 自动重启功能
**新增功能**: 中断触发后自动重启MLX90393，方便反复测试

### 工作流程：
1. 磁场变化触发中断
2. 打印中断消息
3. 设置重启标志位
4. 主循环检测到标志位后执行重启：
   - 关闭MLX90393电源 (MLX90393_PW_OFF)
   - 等待500ms确保完全断电
   - 重新上电 (MLX90393_PW_ON)
   - 重新初始化传感器
   - 重新配置WOC模式
5. 准备下一次中断测试

## 预期输出
```
=== MLX90393 Initialization ===
MLX90393 Init: SUCCESS
MLX90393 WOC Mode: ENABLED
INT pin (PB0) ready for interrupts
Threshold: XY=80, Z=100 LSB

MLX90393: WOC Mode Active

*** MLX90393 INT Triggered! ***
Restarting MLX90393 for next test...

=== Restarting MLX90393 ===
Power OFF
Power ON
MLX90393 Re-Init: SUCCESS
MLX90393 WOC Mode: RE-ENABLED
Ready for next interrupt test
=== Restart Complete ===

MLX90393: WOC Mode Active
```

## 故障排除
1. 如果初始化失败，检查I2C连接和电源
2. 如果没有中断触发，检查PB0连接和阈值设置
3. 如果中断频繁触发，可以增大阈值设置

## 配置参数调整
在 `Inc/mlx90393.h` 中可以调整以下参数：
- `MLX90393_WOC_XY_THRESHOLD`: XY轴变化阈值
- `MLX90393_WOC_Z_THRESHOLD`: Z轴变化阈值
- `MLX90393_WOC_AXES`: 监测轴选择
