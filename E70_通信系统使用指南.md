# E70 通信系统使用指南

## 🎯 系统概述

这是一个完整的E70 LoRa模块通信系统，支持配置管理和设备间通信。

### 系统特点
- **一次性配置**: E70基本设置在生命周期内只执行一次
- **自动模式切换**: 配置完成后自动切换到通信模式
- **标准化协议**: 固定长度的通信数据包
- **可靠验证**: 配置写入后自动验证

## 📦 通信协议

### 重要概念区分
- **E70模块地址**: E70硬件模块的网络地址，用于LoRa组网（可多设备共享）
- **单片机设备ID**: 应用层设备标识，用于业务逻辑识别（每个设备唯一）

### 发送数据包格式 (8字节)
```
+------+------+----------+----------+----------+----------+------+
| 0xFA | 0xFB | MCU_ID_H | MCU_ID_L | VOLT_H   | VOLT_L   | 0xFD |
+------+------+----------+----------+----------+----------+------+
| 包头1| 包头2| 单片机ID高位| 单片机ID低位| 电压高位 | 电压低位 | 结尾 |
```

### 接收ACK格式 (6字节)
```
+------+------+------+------+------+
| 0xFC | 0xFD | 0x55 | 0x66 | 0xFD |
+------+------+------+------+------+
| 包头1| 包头2| ACK1 | ACK2 | 结尾 |
```

## 🔧 API接口

### 1. 初始化函数（生命周期内调用一次）
```c
uint8_t E70_InitializeConfig(uint16_t e70_module_address, uint8_t channel, uint8_t power);
```
- **功能**: 配置E70模块并验证设置
- **参数**:
  - `e70_module_address`: E70模块网络地址 (0x0000-0xFFFF) - 用于LoRa组网
  - `channel`: 信道 (0-31)
  - `power`: 功率等级 (0=14dBm, 1=10dBm, 2=7dBm, 3=4dBm)
- **返回**: 1=成功, 0=失败

### 2. 通信模式函数
```c
uint8_t E70_EnterCommMode(void);
void E70_StartContinuousRx(void);
void E70_StopContinuousRx(void);
```

### 3. 数据发送函数
```c
uint8_t E70_SendDeviceInfo(uint16_t mcu_device_id, uint16_t battery_voltage);
```
- **功能**: 发送单片机设备ID和电池电压
- **参数**:
  - `mcu_device_id`: 单片机设备ID (0x0000-0xFFFF) - 用于业务逻辑识别
  - `battery_voltage`: 电池电压 (单位: mV)
- **返回**: 1=发送成功, 0=发送失败

### 4. 接收检查函数
```c
uint8_t E70_CheckReceiveAck(void);
```
- **功能**: 检查是否收到正确的ACK回复
- **返回**: 1=收到ACK, 0=未收到

## 🚀 使用示例

### 基本初始化
```c
int main(void)
{
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_LPUART1_UART_Init();
    MX_USART2_UART_Init();

    // E70初始化（生命周期内只执行一次）
    uint8_t init_result = E70_InitializeConfig(E70_MODULE_ADDRESS, 10, 1);
    if (!init_result) {
        printf("E70 Init FAILED!\r\n");
        Error_Handler();
    }

    // 进入通信模式
    E70_EnterCommMode();
    E70_StartContinuousRx();

    printf("=== E70 Ready for Communication ===\r\n");

    // 主循环
    while(1) {
        // 根据特定条件发送数据
        if (需要发送数据的条件) {
            uint16_t voltage = 读取电池电压();
            E70_SendDeviceInfo(MCU_DEVICE_ID, voltage);

            // 检查ACK
            HAL_Delay(100);
            if (E70_CheckReceiveAck()) {
                printf("ACK received\r\n");
                // 重新启动接收
                uart_rx_index = 0;
                E70_StartContinuousRx();
            }
        }

        HAL_Delay(100);
    }
}
```

### 设备ID配置
在编译时定义单片机设备ID（每个设备生产时不同）：
```c
// 方法1: 在代码中修改 e70_config.h
#define MCU_DEVICE_ID 0x0001

// 方法2: 在编译选项中定义
// -DMCU_DEVICE_ID=0x0001

// E70模块地址（用于组网，可多设备共享）
#define E70_MODULE_ADDRESS 0x1234
```

## 📊 预期输出

### 初始化阶段
```
E70 Initialize Start
Config OK
Current Config: Addr=0x1234, Ch=10, Pwr=10dBm  // E70模块地址
Entering Comm Mode
Comm Mode Ready
=== E70 Ready for Communication ===
```

### 通信阶段
```
Sent: MCU_ID=0x0001, Volt=3300mV  // 单片机设备ID
ACK Received
```

## ⚠️ 重要注意事项

1. **初始化顺序**: 必须先调用 `E70_InitializeConfig()` 再调用 `E70_EnterCommMode()`

2. **模式切换**: 每次模式切换都会断电重启E70，确保模式正确设置

3. **接收管理**: 发送数据后检查ACK时，需要重新启动连续接收

4. **数据格式**: 所有多字节数据都是高位在前（大端序）

5. **错误处理**: 初始化失败时应该进入错误处理，不要继续执行

## 🔧 自定义配置

### 修改通信协议
如需修改协议格式，在 `e70_config.h` 中修改相关宏定义：
```c
#define E70_SEND_HEADER1        0xFA    // 发送包头1
#define E70_SEND_HEADER2        0xFB    // 发送包头2
#define E70_RECV_HEADER1        0xFC    // 接收包头1
#define E70_RECV_HEADER2        0xFD    // 接收包头2
#define E70_RECV_ACK1           0x55    // ACK字节1
#define E70_RECV_ACK2           0x66    // ACK字节2
#define E70_PACKET_END          0xFD    // 包结尾
```

### 修改发送条件
在主循环中根据实际需求修改发送触发条件：
```c
// 示例条件
if (按键按下 || 定时器到期 || 传感器触发) {
    E70_SendDeviceInfo(DEVICE_ID, battery_voltage);
}
```

---

**版本**: v1.0
**更新日期**: 2025年1月
**状态**: 已验证可用
